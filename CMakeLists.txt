cmake_minimum_required(VERSION 3.10.0)
project(smart_home_system VERSION 1.0.0 LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -g")

# 源文件列表
set(SOURCES
    src/main.c
    src/file_utils.c
    src/temp_humidity_sensor.c
    src/light_sensor.c
    src/current_sensor.c
    src/fan_control.c
    src/data_package.c
    src/mqtt_client.c
)

# 头文件目录
include_directories(include)

# 创建可执行文件
add_executable(smart_home_system ${SOURCES})

# 查找pthread库
find_package(Threads REQUIRED)

# 链接必要的库
target_link_libraries(smart_home_system
    Threads::Threads
)

# 注意：在A7开发板上部署时需要启用以下库链接：
# target_link_libraries(smart_home_system mosquitto Threads::Threads)
# 请确保已安装: sudo apt-get install libmosquitto-dev

# 创建测试可执行文件
add_executable(test_sensors
    tests/test_sensors.c
    src/file_utils.c
    src/temp_humidity_sensor.c
    src/light_sensor.c
    src/current_sensor.c
)
target_include_directories(test_sensors PRIVATE include)

add_executable(test_fan
    tests/test_fan.c
    src/file_utils.c
    src/fan_control.c
)
target_include_directories(test_fan PRIVATE include)

