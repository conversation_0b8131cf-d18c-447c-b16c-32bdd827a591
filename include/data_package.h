#ifndef DATA_PACKAGE_H
#define DATA_PACKAGE_H

#include <stddef.h>
#include "sensors.h"
#include "actuators.h"

/**
 * @file data_package.h
 * @brief 智能家居系统数据封装模块接口定义
 * @details 本模块负责JSON数据的封装、解析和格式转换
 *          完全符合M4规范的JSON格式
 * <AUTHOR> Home System Team
 * @version 1.0.0
 */

// JSON数据包缓冲区最大长度
#define JSON_BUFFER_SIZE 1024

/**
 * @brief 将传感器数据封装成M4规范的JSON字符串
 * @details 按照M4规范格式封装传感器数据:
 *          {"tem":float,"hum":float,"id":0,"light":float,"id":1,"voltage":float,"id":3}
 *          其中id分别对应: 温湿度传感器(0), 光照传感器(1), 电压传感器(3)
 * @param[in] data 传感器数据结构体指针
 * @param[out] json_buffer 输出JSON字符串的缓冲区
 * @param[in] buffer_size 缓冲区大小(字节)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS JSON封装成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针或缓冲区大小为0)
 * @retval ERROR_INVALID_PARAM JSON字符串超出缓冲区大小
 */
int package_sensor_data_to_json(const sensor_data_t* data, char* json_buffer, size_t buffer_size);

/**
 * @brief 将传感器数据和执行器状态封装成完整的M4规范JSON字符串
 * @details 封装格式: {"tem":float,"hum":float,"id":0,"light":float,"id":1,
 *                    "voltage":float,"id":3,"fan":bool,"id":0}
 *          包含所有传感器数据和风扇状态
 * @param[in] sensor_data 传感器数据结构体指针
 * @param[in] fan_status 风扇状态结构体指针
 * @param[out] json_buffer 输出JSON字符串的缓冲区
 * @param[in] buffer_size 缓冲区大小(字节)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS JSON封装成功
 * @retval ERROR_INVALID_PARAM 参数无效(空指针或缓冲区大小为0)
 * @retval ERROR_INVALID_PARAM JSON字符串超出缓冲区大小
 */
int package_complete_data_to_json(const sensor_data_t* sensor_data,
                                  const fan_status_t* fan_status,
                                  char* json_buffer,
                                  size_t buffer_size);

/**
 * @brief 解析来自云端的JSON控制指令
 * @details 支持M4规范格式: {"fan":bool,"id":0,"lamp":bool,"id":0}
 *          同时兼容旧格式: {"fan":"on|off","led":"on|off"}
 *          解析风扇控制指令和LED控制指令
 * @param[in] json_str 待解析的JSON字符串
 * @param[out] fan_command 输出风扇控制指令
 *                        -1=无指令, 0=关闭, 180=开启(中速)
 * @param[out] led_command 输出LED控制指令
 *                        -1=无指令, 0=关闭, 1=开启/闪烁
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS 解析成功(可能部分字段无效)
 * @retval ERROR_INVALID_PARAM 参数无效(空指针)
 */
int parse_control_command(const char* json_str, int* fan_command, int* led_command);

/**
 * @brief 创建操作响应的JSON字符串
 * @details 生成标准格式的响应JSON:
 *          {"timestamp":"YYYY-MM-DD HH:MM:SS","device_id":"xxx",
 *           "status":"success|error","message":"详细信息"}
 * @param[in] success 操作是否成功 (0=失败, 非0=成功)
 * @param[in] message 响应消息内容，可以为NULL(使用默认消息)
 * @param[out] json_buffer 输出JSON字符串的缓冲区
 * @param[in] buffer_size 缓冲区大小(字节)
 * @return 成功返回SUCCESS(0)，失败返回对应错误码
 * @retval SUCCESS JSON创建成功
 * @retval ERROR_INVALID_PARAM 参数无效(json_buffer为空或缓冲区大小为0)
 * @retval ERROR_INVALID_PARAM JSON字符串超出缓冲区大小
 */
int create_response_json(int success, const char* message, char* json_buffer, size_t buffer_size);

#endif // DATA_PACKAGE_H
