#include "actuators.h"
#include "file_utils.h"
#include <unistd.h>

// 全局风扇状态
static fan_status_t g_fan_status = {0, 0, FAN_OFF};

int actuators_init(void) {
    // 检查风扇控制文件是否存在
    if (!file_exists(FAN_CONTROL_PATH)) {
        print_error(__func__, "Fan control file not found", ERROR_FILE_OPEN);
        return ERROR_FILE_OPEN;
    }

    // 初始化时关闭风扇
    int ret = fan_off();
    if (ret != SUCCESS) {
        return ret;
    }

    printf("[INFO] Actuators initialized\n");
    return SUCCESS;
}

int set_fan_speed(int speed) {
    if (speed < 0 || speed > 255) {
        print_error(__func__, "Invalid fan speed", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    int ret = write_sysfs_int(FAN_CONTROL_PATH, speed);
    if (ret != SUCCESS) {
        print_error(__func__, "Failed to set fan speed", ret);
        return ret;
    }

    // 更新风扇状态
    g_fan_status.current_speed = speed;
    g_fan_status.is_running = (speed > 0) ? 1 : 0;

    // 更新速度等级
    if (speed == 0) {
        g_fan_status.level = FAN_OFF;
    } else if (speed <= FAN_SPEED_LOW) {
        g_fan_status.level = FAN_LOW;
    } else if (speed <= FAN_SPEED_MEDIUM) {
        g_fan_status.level = FAN_MEDIUM;
    } else {
        g_fan_status.level = FAN_HIGH;
    }

    return SUCCESS;
}

int set_fan_level(fan_speed_level_t level) {
    int speed;

    switch (level) {
        case FAN_OFF:
            speed = FAN_SPEED_OFF;
            break;
        case FAN_LOW:
            speed = FAN_SPEED_LOW;
            break;
        case FAN_MEDIUM:
            speed = FAN_SPEED_MEDIUM;
            break;
        case FAN_HIGH:
            speed = FAN_SPEED_HIGH;
            break;
        default:
            print_error(__func__, "Invalid fan level", ERROR_INVALID_PARAM);
            return ERROR_INVALID_PARAM;
    }

    return set_fan_speed(speed);
}

int fan_on(fan_speed_level_t level) {
    if (level == FAN_OFF) {
        level = FAN_MEDIUM; // 默认中速
    }
    return set_fan_level(level);
}

int fan_off(void) {
    return set_fan_speed(FAN_SPEED_OFF);
}

int get_fan_status(fan_status_t* status) {
    if (!status) {
        print_error(__func__, "Invalid parameter", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    *status = g_fan_status;
    return SUCCESS;
}

void print_fan_status(const fan_status_t* status) {
    if (!status) {
        printf("[ERROR] Invalid fan status pointer\n");
        return;
    }

    const char* level_str;
    switch (status->level) {
        case FAN_OFF:    level_str = "OFF"; break;
        case FAN_LOW:    level_str = "LOW"; break;
        case FAN_MEDIUM: level_str = "MEDIUM"; break;
        case FAN_HIGH:   level_str = "HIGH"; break;
        default:         level_str = "UNKNOWN"; break;
    }

    printf("=== Fan Status ===\n");
    printf("Running: %s\n", status->is_running ? "YES" : "NO");
    printf("Speed: %d/255\n", status->current_speed);
    printf("Level: %s\n", level_str);
    printf("==================\n");
}

int set_led_state(int led_num, int state) {
    const char* led_path;
    
    switch (led_num) {
        case 1: led_path = LED1_PATH; break;
        case 2: led_path = LED2_PATH; break;
        case 3: led_path = LED3_PATH; break;
        default:
            print_error(__func__, "Invalid LED number", ERROR_INVALID_PARAM);
            return ERROR_INVALID_PARAM;
    }

    return write_sysfs_int(led_path, state ? 1 : 0);
}

int led_all_off(void) {
    int ret;
    
    ret = set_led_state(1, 0);
    if (ret != SUCCESS) return ret;
    
    ret = set_led_state(2, 0);
    if (ret != SUCCESS) return ret;
    
    ret = set_led_state(3, 0);
    if (ret != SUCCESS) return ret;

    return SUCCESS;
}

int led_blink(int times, int interval_ms) {
    if (times <= 0 || interval_ms <= 0) {
        print_error(__func__, "Invalid parameters", ERROR_INVALID_PARAM);
        return ERROR_INVALID_PARAM;
    }

    for (int i = 0; i < times; i++) {
        // 开启所有LED
        set_led_state(1, 1);
        set_led_state(2, 1);
        set_led_state(3, 1);
        
        usleep(interval_ms * 1000); // 转换为微秒

        // 关闭所有LED
        led_all_off();
        
        if (i < times - 1) { // 最后一次不需要等待
            usleep(interval_ms * 1000);
        }
    }

    return SUCCESS;
}
