#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>
#include <time.h>
#include <string.h>

#include "smart_home_config.h"
#include "sensors.h"
#include "actuators.h"
#include "data_package.h"
#include "mqtt_client.h"

// 全局运行标志
static volatile int g_running = 1;

// 系统状态
typedef struct {
    sensor_data_t sensor_data;
    fan_status_t fan_status;
    int auto_control_enabled;
    time_t last_data_collection;
    time_t last_mqtt_publish;
} system_state_t;

static system_state_t g_system_state = {0};

// 信号处理函数
void signal_handler(int sig) {
    printf("\n[INFO] Received signal %d, shutting down gracefully...\n", sig);
    g_running = 0;
}

// MQTT消息回调函数
void on_mqtt_message(const mqtt_message_t* message) {
    if (!message) return;

    printf("[MQTT] Received command: %s\n", message->payload);

    int fan_speed = -1;
    int led_state = -1;
    
    int ret = parse_control_command(message->payload, &fan_speed, &led_state);
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to parse command\n");
        return;
    }

    char response_msg[256] = "Commands executed: ";
    int success = 1;

    // 处理风扇指令
    if (fan_speed >= 0) {
        if (set_fan_speed(fan_speed) == SUCCESS) {
            snprintf(response_msg + strlen(response_msg), 256 - strlen(response_msg),
                    "Fan=%d; ", fan_speed);
        } else {
            success = 0;
            strcat(response_msg, "Fan failed; ");
        }
    }

    // 处理LED指令（仅开关控制）
    if (led_state != -1) {
        if (led_state == 0) {
            if (led_all_off() == SUCCESS) {
                strcat(response_msg, "LEDs off; ");
            } else {
                success = 0;
                strcat(response_msg, "LEDs off failed; ");
            }
        } else {
            if (set_led_state(1, 1) == SUCCESS && 
                set_led_state(2, 1) == SUCCESS && 
                set_led_state(3, 1) == SUCCESS) {
                strcat(response_msg, "LEDs on; ");
            } else {
                success = 0;
                strcat(response_msg, "LEDs on failed; ");
            }
        }
    }

    // 发送响应到元宇宙平台
    char response_buffer[JSON_BUFFER_SIZE];
    if (create_response_json(success, response_msg, response_buffer, sizeof(response_buffer)) == SUCCESS) {
        mqtt_publish_response(response_buffer);
    }


}


// 自动控制逻辑
void auto_control_logic(const sensor_data_t* data) {
    // 如果5秒内有远程控制指令，则跳过自动控制
    static time_t last_remote_control = 0;
    if (time(NULL) - last_remote_control < 5) {
        return;
    }
    
    if (!g_system_state.auto_control_enabled) {
        return;
    }
   

    static int last_fan_decision = -1;
    int current_fan_decision = 0;

    // 温度控制逻辑
    if (data->temperature > TEMP_THRESHOLD) {
        current_fan_decision = 1; // 需要开启风扇
        
        if (last_fan_decision != current_fan_decision) {
            printf("[AUTO] Temperature %.2f°C > %.2f°C, turning on fan\n", 
                   data->temperature, TEMP_THRESHOLD);
            set_fan_level(FAN_MEDIUM);
        }
    } else if (data->temperature < TEMP_THRESHOLD - 2.0f) { // 添加2度的滞后
        current_fan_decision = 0; // 可以关闭风扇
        
        if (last_fan_decision != current_fan_decision) {
            printf("[AUTO] Temperature %.2f°C < %.2f°C, turning off fan\n", 
                   data->temperature, TEMP_THRESHOLD - 2.0f);
            fan_off();
        }
    }

    last_fan_decision = current_fan_decision;

    // 湿度警告逻辑
    static time_t last_humidity_warning = 0;
    time_t now = time(NULL);
    
    if (data->humidity > HUMIDITY_THRESHOLD && 
        (now - last_humidity_warning) > 60) { // 至少间隔60秒
        printf("[AUTO] Humidity %.2f%% > %.2f%%, LED warning\n", 
               data->humidity, HUMIDITY_THRESHOLD);
        led_blink(5, 300); // 闪烁5次，每次300ms
        last_humidity_warning = now;
    }
    last_remote_control = 0;
}

// 初始化系统
int system_init(void) {
    printf("=== Smart Home System Initialization ===\n");

    // 初始化传感器
    int ret = sensors_init();
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to initialize sensors\n");
        return ret;
    }

    // 初始化执行器
    ret = actuators_init();
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to initialize actuators\n");
        return ret;
    }

    // 初始化MQTT客户端
    ret = mqtt_init();
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to initialize MQTT client\n");
        return ret;
    }

    // 连接MQTT服务器
    ret = mqtt_connect();
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to connect to MQTT broker\n");
        return ret;
    }

    // 设置MQTT消息回调
    mqtt_set_message_callback(on_mqtt_message);

    // 订阅控制主题
    ret = mqtt_subscribe(MQTT_SUB_TOPIC);
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to subscribe to control topic\n");
        return ret;
    }

    // 启动MQTT循环
    ret = mqtt_loop_start();
    if (ret != SUCCESS) {
        printf("[ERROR] Failed to start MQTT loop\n");
        return ret;
    }

    // 初始化系统状态
    g_system_state.auto_control_enabled = 1;
    g_system_state.last_data_collection = 0;
    g_system_state.last_mqtt_publish = 0;

    printf("[INFO] System initialized successfully\n");
    printf("========================================\n\n");

    return SUCCESS;
}

// 清理系统资源
void system_cleanup(void) {
    printf("\n=== System Cleanup ===\n");
    
    // 关闭所有执行器
    fan_off();
    led_all_off();
    
    // 清理MQTT客户端
    mqtt_cleanup();
    
    printf("[INFO] System cleanup completed\n");
}

int main(void) {
    printf("🏠 Smart Home System v%s 🏠\n", SYSTEM_VERSION);
    printf("Device ID: %s\n", MQTT_CLIENT_ID);
    printf("Running in HARDWARE MODE\n");
    printf("=====================================\n\n");

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化系统
    if (system_init() != SUCCESS) {
        printf("[ERROR] System initialization failed\n");
        return EXIT_FAILURE;
    }

    // 主循环
    printf("[INFO] Starting main loop...\n");
    char json_buffer[JSON_BUFFER_SIZE];
    
    while (g_running) {
        time_t now = time(NULL);

        // 数据采集
        if ((now - g_system_state.last_data_collection) >= DATA_COLLECTION_INTERVAL) {
            int ret = read_all_sensors(&g_system_state.sensor_data);
            if (ret == SUCCESS) {
                printf("\n[DATA] Sensor data collected:\n");
                print_sensor_data(&g_system_state.sensor_data);
                
                // 更新风扇状态
                get_fan_status(&g_system_state.fan_status);
                
                // 执行自动控制逻辑
                auto_control_logic(&g_system_state.sensor_data);
                
                g_system_state.last_data_collection = now;
            } else {
                printf("[ERROR] Failed to read sensor data\n");
            }
        }

        // MQTT数据发布
        if ((now - g_system_state.last_mqtt_publish) >= MQTT_PUBLISH_INTERVAL) {
            if (package_complete_data_to_json(&g_system_state.sensor_data,
                                              &g_system_state.fan_status,
                                              json_buffer,
                                              sizeof(json_buffer)) == SUCCESS) {
                mqtt_publish_sensor_data(json_buffer);
                g_system_state.last_mqtt_publish = now;
            } else {
                printf("[ERROR] Failed to package data to JSON\n");
            }
        }

        // 短暂休眠，避免CPU占用过高
        sleep(1);
    }

    // 清理资源
    system_cleanup();
    
    printf("\n[INFO] Smart Home System shutdown completed\n");
    return EXIT_SUCCESS;
}
