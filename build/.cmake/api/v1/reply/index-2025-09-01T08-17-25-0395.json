{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.18"}, "version": {"isDirty": false, "major": 3, "minor": 18, "patch": 4, "string": "3.18.4", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-4e5bf78c0016ab724900.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}, {"jsonFile": "cache-v2-eb61f4154b9ac3c4a831.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-66a9a310406a8a3bf7a2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-eb61f4154b9ac3c4a831.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-4e5bf78c0016ab724900.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}, {"error": "unknown request kind 'toolchains'"}, {"jsonFile": "cmakeFiles-v1-66a9a310406a8a3bf7a2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}