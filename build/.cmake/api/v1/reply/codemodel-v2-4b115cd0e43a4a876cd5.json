{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "smart_home_system", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "smart_home_system::@6890427a1f51a3e7e1df", "jsonFile": "target-smart_home_system-Debug-be9648fa828e83333f31.json", "name": "smart_home_system", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_fan::@6890427a1f51a3e7e1df", "jsonFile": "target-test_fan-Debug-87fcf9ded23cd1e7271c.json", "name": "test_fan", "projectIndex": 0}, {"directoryIndex": 0, "id": "test_sensors::@6890427a1f51a3e7e1df", "jsonFile": "target-test_sensors-Debug-96ec9f69381ba90cf535.json", "name": "test_sensors", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/smart_home/build", "source": "/home/<USER>/smart_home"}, "version": {"major": 2, "minor": 1}}