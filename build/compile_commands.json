[{"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/tests/test_fan.c.o -c /home/<USER>/smart_home/tests/test_fan.c", "file": "/home/<USER>/smart_home/tests/test_fan.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/src/file_utils.c.o -c /home/<USER>/smart_home/src/file_utils.c", "file": "/home/<USER>/smart_home/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_fan.dir/src/fan_control.c.o -c /home/<USER>/smart_home/src/fan_control.c", "file": "/home/<USER>/smart_home/src/fan_control.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/tests/test_sensors.c.o -c /home/<USER>/smart_home/tests/test_sensors.c", "file": "/home/<USER>/smart_home/tests/test_sensors.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/file_utils.c.o -c /home/<USER>/smart_home/src/file_utils.c", "file": "/home/<USER>/smart_home/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home/src/temp_humidity_sensor.c", "file": "/home/<USER>/smart_home/src/temp_humidity_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/light_sensor.c.o -c /home/<USER>/smart_home/src/light_sensor.c", "file": "/home/<USER>/smart_home/src/light_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/test_sensors.dir/src/current_sensor.c.o -c /home/<USER>/smart_home/src/current_sensor.c", "file": "/home/<USER>/smart_home/src/current_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/main.c.o -c /home/<USER>/smart_home/src/main.c", "file": "/home/<USER>/smart_home/src/main.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/file_utils.c.o -c /home/<USER>/smart_home/src/file_utils.c", "file": "/home/<USER>/smart_home/src/file_utils.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/temp_humidity_sensor.c.o -c /home/<USER>/smart_home/src/temp_humidity_sensor.c", "file": "/home/<USER>/smart_home/src/temp_humidity_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/light_sensor.c.o -c /home/<USER>/smart_home/src/light_sensor.c", "file": "/home/<USER>/smart_home/src/light_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/current_sensor.c.o -c /home/<USER>/smart_home/src/current_sensor.c", "file": "/home/<USER>/smart_home/src/current_sensor.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/fan_control.c.o -c /home/<USER>/smart_home/src/fan_control.c", "file": "/home/<USER>/smart_home/src/fan_control.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/data_package.c.o -c /home/<USER>/smart_home/src/data_package.c", "file": "/home/<USER>/smart_home/src/data_package.c"}, {"directory": "/home/<USER>/smart_home/build", "command": "/usr/bin/arm-linux-gnueabihf-gcc -I../include  -Wall -Wextra -g -g -std=gnu99 -o CMakeFiles/smart_home_system.dir/src/mqtt_client.c.o -c /home/<USER>/smart_home/src/mqtt_client.c", "file": "/home/<USER>/smart_home/src/mqtt_client.c"}]